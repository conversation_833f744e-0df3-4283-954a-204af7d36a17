{"overall_metrics": {"accuracy": 73.78769230769231, "macro_f1": 73.78767760426797, "kappa": 0.727392}, "model_complexity": {"macs": "1.250G", "parameters": "3.703M", "macs_raw": 1250251264.0, "params_raw": 3702568.0}, "inference_performance": {"avg_inference_time_ms": 0.05934795874815721, "std_inference_time_ms": 0.031453028652380616, "min_inference_time_ms": 0.03499910235404968, "max_inference_time_ms": 1.864016056060791}, "dataset_info": {"total_samples": 260000, "dataset_type": "hisar", "input_shape": [2, 1024], "num_classes": 26, "snr_range": [-20.0, 18.0]}, "test_info": {"model_path": "./saved_models/awn/hisar_20250619_001823/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-20 10:49:01"}}