import os
import json
import pandas as pd
from pathlib import Path
import glob

# ==================== 配置区域 ====================
# 在这里自定义要处理的模型文件夹和数据集类型
#
# 使用说明：
# 1. 手动配置：修改下面的 MODEL_FOLDERS 和 DATASETS 列表
# 2. 自动检测：将 AUTO_DETECT_MODELS 设为 True，脚本会自动扫描当前目录下包含 saved_models 的文件夹
# 3. Excel表格中的模型顺序将按照 MODEL_FOLDERS 列表的顺序显示

# 定义要处理的模型文件夹（根据实际存在的文件夹修改）
MODEL_FOLDERS = [
    'CLDNN-new',
    'IC-AMCNet-new',
    'AWN-new',
    'MAMC-new',
    'MAWDN',
    'WNN-MRNN'
]

# 定义要处理的数据集类型（根据实际训练的数据集修改）
DATASETS = ['rml201801a', 'rml', 'torchsig1024', 'torchsig2048', 'torchsig4096']

# 定义各数据集的SNR范围（根据配置文件中的snr_ranges）
DATASET_SNR_RANGES = {
    'rml': [-20, 18],           # RML数据集SNR范围: -20dB到18dB，步长2dB
    'rml201801a': [-20, 30],    # RML2018.01a数据集SNR范围: -20dB到30dB，步长2dB
    'torchsig1024': [0, 30],    # TorchSig1024数据集SNR范围: 0dB到30dB，步长2dB
    'torchsig2048': [0, 30],    # TorchSig2048数据集SNR范围: 0dB到30dB，步长2dB
    'torchsig4096': [0, 30]     # TorchSig4096数据集SNR范围: 0dB到30dB，步长2dB
}

# 输出文件夹名称
OUTPUT_FOLDER = 'snr_results_tables'

# 是否自动检测模型文件夹（设为True时会自动扫描当前目录下的所有文件夹）
AUTO_DETECT_MODELS = True

# ==================== 配置区域结束 ====================

def auto_detect_model_folders():
    """自动检测当前目录下包含saved_models子目录的文件夹"""
    detected_folders = []
    current_dir = os.getcwd()

    for item in os.listdir(current_dir):
        item_path = os.path.join(current_dir, item)
        if os.path.isdir(item_path):
            saved_models_path = os.path.join(item_path, 'saved_models')
            if os.path.exists(saved_models_path):
                detected_folders.append(item)

    return sorted(detected_folders)

def find_snr_results_files():
    """扫描所有模型文件夹，查找snr_results.json文件"""

    # 确定要使用的模型文件夹列表
    if AUTO_DETECT_MODELS:
        model_folders = auto_detect_model_folders()
        print(f"自动检测到的模型文件夹: {model_folders}")
    else:
        model_folders = MODEL_FOLDERS
        print(f"使用配置的模型文件夹: {model_folders}")

    # 存储找到的文件信息
    results = {}

    for dataset in DATASETS:
        results[dataset] = {}

        for model_folder in model_folders:
            results[dataset][model_folder] = None
            
            # 查找saved_models目录
            saved_models_path = os.path.join(model_folder, 'saved_models')
            if not os.path.exists(saved_models_path):
                print(f"警告: {model_folder} 中未找到 saved_models 目录")
                continue
            
            # 查找模型特定的子目录
            model_subdir = None
            for item in os.listdir(saved_models_path):
                item_path = os.path.join(saved_models_path, item)
                if os.path.isdir(item_path):
                    model_subdir = item_path
                    break
            
            if not model_subdir:
                print(f"警告: {model_folder}/saved_models 中未找到模型子目录")
                continue
            
            # 查找数据集特定的测试目录
            test_dirs = []
            for item in os.listdir(model_subdir):
                if item.startswith(f"test_{dataset}_"):
                    test_dirs.append(item)
            
            if not test_dirs:
                print(f"信息: {model_folder} 中未找到 {dataset} 数据集的测试结果")
                continue
            
            # 选择最新的测试目录（按时间戳排序）
            test_dirs.sort(reverse=True)
            latest_dir = test_dirs[0]
            
            # 查找snr_results.json文件
            snr_file = os.path.join(model_subdir, latest_dir, 'plots', 'snr_results.json')
            
            if os.path.exists(snr_file):
                results[dataset][model_folder] = snr_file
                print(f"找到: {model_folder} - {dataset} - {snr_file}")
            else:
                print(f"警告: 未找到 {snr_file}")
    
    return results

def extract_snr_data_from_json(json_file_path):
    """从snr_results.json文件中提取SNR数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取SNR数据，格式为 {SNR值: 准确率}
        snr_data = {}
        for snr_key, metrics in data.items():
            if snr_key.startswith('SNR_'):
                # 提取SNR值，例如从 'SNR_-20dB' 提取 '-20dB'
                snr_value = snr_key.replace('SNR_', '')
                accuracy = metrics.get('accuracy', 0)
                snr_data[snr_value] = accuracy
        
        return snr_data
        
    except Exception as e:
        print(f"错误: 无法读取 {json_file_path}: {e}")
        return None

def get_dataset_snr_values(dataset):
    """根据数据集类型获取对应的SNR值列表"""
    if dataset not in DATASET_SNR_RANGES:
        print(f"警告: 未找到数据集 {dataset} 的SNR范围配置，使用默认范围")
        return []

    snr_min, snr_max = DATASET_SNR_RANGES[dataset]
    # 生成SNR值列表，步长为2dB
    snr_values = []
    for snr in range(snr_min, snr_max + 1, 2):
        snr_values.append(f"{snr}dB")

    return snr_values

def create_output_folder():
    """创建输出文件夹"""
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"创建输出文件夹: {OUTPUT_FOLDER}")
    else:
        print(f"输出文件夹已存在: {OUTPUT_FOLDER}")

def create_snr_excel_tables(results_data):
    """为每个数据集创建SNR准确率Excel表格"""

    # 创建输出文件夹
    create_output_folder()

    # 确定要使用的模型文件夹列表（与find_snr_results_files中的逻辑保持一致）
    if AUTO_DETECT_MODELS:
        model_folders = auto_detect_model_folders()
    else:
        model_folders = MODEL_FOLDERS

    for dataset in results_data.keys():
        print(f"\n创建 {dataset} 数据集的SNR准确率Excel表格...")

        # 获取该数据集的SNR值列表
        dataset_snr_values = get_dataset_snr_values(dataset)
        if not dataset_snr_values:
            print(f"跳过数据集 {dataset}：无法获取SNR范围")
            continue

        print(f"{dataset} 数据集的SNR范围: {dataset_snr_values}")

        # 创建列名：模型名称 + 该数据集的SNR值 (使用英文列名)
        columns = ['Model Name'] + dataset_snr_values

        # 创建DataFrame
        rows = []

        for model in model_folders:
            row = [model]  # 第一列是模型名称

            if model in results_data[dataset] and results_data[dataset][model]:
                # 提取SNR数据
                json_file = results_data[dataset][model]
                snr_data = extract_snr_data_from_json(json_file)

                if snr_data:
                    # 为每个SNR值填入对应的准确率
                    for snr_value in dataset_snr_values:
                        accuracy = snr_data.get(snr_value, '')  # 如果没有该SNR值，填入空字符串
                        row.append(accuracy)
                else:
                    # 数据提取失败，所有SNR列填入空字符串
                    row.extend([''] * len(dataset_snr_values))
            else:
                # 没有找到对应的结果文件，所有SNR列填入空字符串
                row.extend([''] * len(dataset_snr_values))

            rows.append(row)

        # 创建DataFrame
        df = pd.DataFrame(rows, columns=columns)

        # 保存为Excel文件到输出文件夹
        excel_filename = os.path.join(OUTPUT_FOLDER, f"snr_results_{dataset}.xlsx")
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        print(f"已保存: {excel_filename}")

def main():
    """主函数"""
    print("=" * 60)
    print("SNR结果提取工具")
    print("=" * 60)
    print("开始扫描SNR结果文件...")

    # 显示数据集SNR范围配置
    print("\n数据集SNR范围配置:")
    for dataset, snr_range in DATASET_SNR_RANGES.items():
        snr_values = get_dataset_snr_values(dataset)
        print(f"  {dataset}: {snr_range[0]}dB 到 {snr_range[1]}dB (共{len(snr_values)}个SNR值)")

    # 查找所有snr_results.json文件
    results_data = find_snr_results_files()

    print(f"\n扫描完成，开始创建Excel表格...")

    # 创建Excel表格
    create_snr_excel_tables(results_data)

    print(f"\n=" * 60)
    print(f"所有SNR准确率Excel表格创建完成！")
    print(f"结果保存在文件夹: {OUTPUT_FOLDER}")
    print("=" * 60)

if __name__ == '__main__':
    main()
